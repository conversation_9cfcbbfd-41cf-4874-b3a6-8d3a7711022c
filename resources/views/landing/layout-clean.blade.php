<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title ?? 'PT Digital Decision Indonesia' }}</title>
    
    @php
        $companyName = $settings['company_name'] ?? 'PT Digital Decision Indonesia';
        $companyTagline = $settings['company_tagline'] ?? 'Leading ERP Solutions & Business Automation';
        $companyDescription = $settings['company_description'] ?? 'Premier software house untuk solusi ERP custom dan business automation. Official Odoo partner pertama di Riau.';
    @endphp
    
    <meta name="description" content="{{ $settings['site_description'] ?? $companyDescription }}">
    <meta name="keywords" content="ERP, Laravel, Filament, Business Automation, Software House, Odoo Partner">
    <meta name="author" content="{{ $companyName }}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('logo.png') }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom Styles -->
    <style>
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .mobile-menu {
            display: none;
        }
        
        .mobile-menu.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .mobile-menu.active {
                display: block;
            }
        }
    </style>
    
    @stack('styles')
</head>
<body class="font-sans antialiased">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="#home" class="flex items-center">
                        <img class="h-8 w-auto mr-3" src="{{ asset('logo.png') }}" alt="{{ $companyName }}">
                        <span class="text-xl font-semibold text-gray-900">{{ $companyName }}</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    @if(isset($navigation) && $navigation->count() > 0)
                        @foreach($navigation as $item)
                            <a href="{{ $item->href }}"
                               class="text-gray-700 hover:text-primary-600 text-sm font-medium transition-colors duration-200"
                               @if($item->target === '_blank') target="_blank" @endif>
                                {{ $item->label }}
                            </a>
                        @endforeach
                    @else
                        <a href="#home" class="text-gray-700 hover:text-primary-600 text-sm font-medium">Home</a>
                        <a href="#services" class="text-gray-700 hover:text-primary-600 text-sm font-medium">Services</a>
                        <a href="#about" class="text-gray-700 hover:text-primary-600 text-sm font-medium">About</a>
                        <a href="#portfolio" class="text-gray-700 hover:text-primary-600 text-sm font-medium">Portfolio</a>
                        <a href="#contact" class="text-gray-700 hover:text-primary-600 text-sm font-medium">Contact</a>
                    @endif
                </div>

                <!-- Contact Info -->
                <div class="hidden md:flex items-center space-x-4">
                    @php
                        $contactPhone = isset($settings['contact_phone']) ? $settings['contact_phone'] : '+6282171469407';
                        $contactEmail = isset($settings['contact_email']) ? $settings['contact_email'] : '<EMAIL>';
                    @endphp
                    <a href="tel:{{ $contactPhone }}" class="text-sm text-gray-600 hover:text-primary-600">
                        {{ $contactPhone }}
                    </a>
                    <a href="mailto:{{ $contactEmail }}" class="bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors duration-200">
                        Get Quote
                    </a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="mobile-menu-button text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600" aria-label="Toggle menu">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="mobile-menu hidden md:hidden">
            <div class="px-4 py-4 space-y-2 bg-white border-t border-gray-100">
                @if(isset($navigation) && $navigation->count() > 0)
                    @foreach($navigation as $item)
                        <a href="{{ $item->href }}"
                           class="block text-gray-700 hover:text-primary-600 py-2 text-base font-medium transition-colors duration-200"
                           @if($item->target === '_blank') target="_blank" @endif>
                            {{ $item->label }}
                        </a>
                    @endforeach
                @else
                    <a href="#home" class="block text-gray-700 hover:text-primary-600 py-2">Home</a>
                    <a href="#services" class="block text-gray-700 hover:text-primary-600 py-2">Services</a>
                    <a href="#about" class="block text-gray-700 hover:text-primary-600 py-2">About</a>
                    <a href="#portfolio" class="block text-gray-700 hover:text-primary-600 py-2">Portfolio</a>
                    <a href="#contact" class="block text-gray-700 hover:text-primary-600 py-2">Contact</a>
                @endif
                
                <div class="pt-4 mt-4 border-t border-gray-200 space-y-3">
                    <a href="tel:{{ $contactPhone }}"
                       class="block text-gray-600 hover:text-primary-600 py-2 text-sm">
                        {{ $contactPhone }}
                    </a>
                    <a href="mailto:{{ $contactEmail }}"
                       class="block bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary-700 text-center">
                        Get Quote
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="md:col-span-2">
                    <div class="flex items-center mb-4">
                        <img class="h-8 w-auto mr-3" src="{{ asset('logo.png') }}" alt="{{ $companyName }}">
                        <span class="text-xl font-semibold">{{ $companyName }}</span>
                    </div>
                    <p class="text-gray-300 mb-4">{{ $companyDescription }}</p>
                    <div class="flex space-x-4">
                        @if(isset($settings['social_facebook']) && $settings['social_facebook'] !== '#')
                            <a href="{{ $settings['social_facebook'] }}" class="text-gray-300 hover:text-white" target="_blank">
                                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </a>
                        @endif

                        @if(isset($settings['social_instagram']) && $settings['social_instagram'] !== '#')
                            <a href="{{ $settings['social_instagram'] }}" class="text-gray-300 hover:text-white" target="_blank">
                                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297z"/>
                                </svg>
                            </a>
                        @endif

                        @if(isset($settings['social_linkedin']) && $settings['social_linkedin'] !== '#')
                            <a href="{{ $settings['social_linkedin'] }}" class="text-gray-300 hover:text-white" target="_blank">
                                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </a>
                        @endif

                        @if(isset($settings['social_twitter']) && $settings['social_twitter'] !== '#')
                            <a href="{{ $settings['social_twitter'] }}" class="text-gray-300 hover:text-white" target="_blank">
                                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                        @endif
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Kontak</h3>
                    <div class="space-y-2 text-gray-300">
                        <p>{{ $settings['contact_address'] ?? 'Jalan Rawa Mulya No 3A, Sidomulyo Timur, Marpoyan Damai, Pekanbaru' }}</p>
                        <p>{{ $contactPhone }}</p>
                        <p>{{ $contactEmail }}</p>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Layanan</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#services" class="hover:text-white">ERP Development</a></li>
                        <li><a href="#services" class="hover:text-white">Business Automation</a></li>
                        <li><a href="#services" class="hover:text-white">Odoo Implementation</a></li>
                        <li><a href="#services" class="hover:text-white">Custom Software</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; {{ date('Y') }} {{ $companyName }}. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.querySelector('.mobile-menu-button');
            const mobileMenu = document.querySelector('.mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('active');
                });
            }

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>

    @stack('scripts')
</body>
</html>
