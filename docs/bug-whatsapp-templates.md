# Bug WhatsApp Templates Documentation

## Overview

Sistem Bug WhatsApp Templates memungkinkan pengelolaan template notifikasi WhatsApp untuk bug secara dinamis melalui database. Sistem ini terintegrasi dengan sistem bug tracking dan akan mengirim notifikasi otomatis ketika ada perubahan status bug.

## Features

### 1. Dynamic Template Management
- Template disimpan di database dan dapat dikelola melalui admin panel
- Support untuk multiple template types: `bug_solved`, `bug_reported`, `bug_assigned`
- Template menggunakan variable placeholders yang dapat diganti secara dinamis
- Support untuk multiple languages (Indonesian/English)

### 2. Bug Types
Sistem mendukung berbagai jenis bug:
- **UI/UX Issue** - Masalah tampilan dan pengalaman pengguna
- **Functionality** - Masalah fungsionalitas aplikasi
- **Performance** - Masalah performa aplikasi
- **Security** - <PERSON><PERSON><PERSON> keamanan
- **Data Issue** - Masalah data
- **Integration** - <PERSON><PERSON><PERSON> integrasi
- **Compatibility** - <PERSON><PERSON><PERSON> kompatibilitas
- **Other** - Lainnya

### 3. Automatic Notifications
Sistem akan otomatis mengirim notifikasi WhatsApp ketika:
- Bug baru dilaporkan (`bug_reported`)
- Bug di-assign ke developer (`bug_assigned`)
- Bug selesai diperbaiki (`bug_solved`)

## Database Structure

### WhatsApp Message Templates Table
```sql
whats_app_message_templates:
- id
- name (string)
- type (enum: bug_solved, bug_reported, bug_assigned, etc.)
- template (text) - Template dengan variable placeholders
- variables (json) - Array of available variables
- description (text)
- is_active (boolean)
- is_default (boolean)
- category (string: bug, task, general, etc.)
- language (string: id, en)
- created_by (foreign key to users)
- timestamps
```

### Bugs Table (Updated)
```sql
bugs:
- id
- project_id (foreign key)
- panel (string)
- type (enum: ui, functionality, performance, security, data, integration, compatibility, other)
- fitur (string)
- deskripsi (text)
- link (string)
- bukti (json)
- checked_by (foreign key to employees)
- date_issued (date)
- status (enum)
- done_by (foreign key to employees)
- solved_date (date)
- validasi (string)
- notes (text)
- created_by (foreign key to users)
- timestamps
```

## Available Template Variables

### Bug Solved Template Variables
- `greeting` - Time-based greeting (Selamat Pagi/Siang/Sore/Malam)
- `bug_header` - Header message
- `bug_fitur` - Bug feature name
- `bug_type` - Bug type label
- `bug_panel` - Panel (Admin/Karyawan)
- `project_name` - Project name
- `solved_by` - Name of person who solved the bug
- `solved_date` - Date when bug was solved
- `bug_description` - Bug description
- `bug_link` - Bug link (if any)
- `celebration` - Celebration message
- `encouragement` - Encouragement message
- `footer` - Footer message

### Bug Reported Template Variables
- `greeting` - Time-based greeting
- `bug_fitur` - Bug feature name
- `bug_type` - Bug type label
- `bug_panel` - Panel (Admin/Karyawan)
- `project_name` - Project name
- `reported_by` - Name of person who reported the bug
- `report_date` - Date when bug was reported
- `bug_description` - Bug description
- `bug_link` - Bug link (if any)
- `priority_message` - Priority message based on bug type
- `footer` - Footer message

### Bug Assignment Template Variables
- `greeting` - Time-based greeting
- `bug_fitur` - Bug feature name
- `bug_type` - Bug type label
- `bug_panel` - Panel (Admin/Karyawan)
- `project_name` - Project name
- `assigned_to` - Name of person assigned to fix the bug
- `assigned_by` - Name of person who assigned the bug
- `bug_description` - Bug description
- `bug_link` - Bug link (if any)
- `motivation` - Motivation message
- `footer` - Footer message

## Default Templates

### Bug Solved Template
```
{greeting}
🎉 *BUG SOLVED!* 🎉

🐛 *Bug:* {bug_fitur}
🏷️ *Type:* {bug_type}
📱 *Panel:* {bug_panel}
🎯 *Project:* {project_name}

✅ *Solved by:* {solved_by}
📅 *Solved Date:* {solved_date}

📄 *Description:*
_{bug_description}_

🔗 *Link:* {bug_link}

{celebration}
{encouragement}
{footer}
```

### Bug Reported Template
```
{greeting}
🚨 *BUG REPORT* 🚨

🐛 *Bug:* {bug_fitur}
🏷️ *Type:* {bug_type}
📱 *Panel:* {bug_panel}
🎯 *Project:* {project_name}

👤 *Reported by:* {reported_by}
📅 *Report Date:* {report_date}

📄 *Description:*
_{bug_description}_

🔗 *Link:* {bug_link}

{priority_message}
{footer}
```

### Bug Assignment Template
```
{greeting}
🔧 *BUG ASSIGNMENT* 🔧

🐛 *Bug:* {bug_fitur}
🏷️ *Type:* {bug_type}
📱 *Panel:* {bug_panel}
🎯 *Project:* {project_name}

👤 *Assigned to:* {assigned_to}
👨‍💼 *Assigned by:* {assigned_by}

📄 *Description:*
_{bug_description}_

🔗 *Link:* {bug_link}

{motivation}
{footer}
```

## Usage

### 1. Managing Templates
Templates dapat dikelola melalui admin panel di menu **WhatsApp Message Templates**:
- Create new templates
- Edit existing templates
- Duplicate templates
- Activate/deactivate templates
- Set default templates

### 2. Seeding Default Templates
Untuk membuat default templates:
```php
php artisan tinker
App\Models\WhatsAppMessageTemplate::seedDefaults();
```

### 3. Manual Notification Sending
```php
use App\Services\BugWhatsAppService;

$bugWhatsAppService = app(BugWhatsAppService::class);

// Send bug solved notification
$bugWhatsAppService->sendBugSolvedNotification($bug);

// Send bug reported notification
$bugWhatsAppService->sendBugReportedNotification($bug);

// Send bug assignment notification
$bugWhatsAppService->sendBugAssignmentNotification($bug);
```

## Configuration

### WhatsApp Configuration
Pastikan WhatsApp configuration sudah diatur di menu **WhatsApp Config**:
- API URL
- API Key
- Sender Number
- Group ID
- Active status

### Environment Variables
```env
WHATSAPP_NOTIFICATIONS_ENABLED=true
WHATSAPP_BUG_SOLVED_ENABLED=true
```

## Technical Implementation

### Services
- `BugWhatsAppService` - Main service for sending bug WhatsApp notifications
- `WhatsAppMessageTemplates` - Template rendering and management

### Events & Listeners
- `BugSolved` event - Triggered when bug status changes to completed
- `SendBugSolvedNotification` listener - Handles bug solved WhatsApp notifications

### Observers
- `BugObserver` - Handles automatic notifications for bug creation and assignment

### Models
- `WhatsAppMessageTemplate` - Template management
- `Bug` - Extended with type field and helper methods
- `WhatsAppNotificationLog` - Logging notifications

## Troubleshooting

### Common Issues
1. **Templates not found**: Ensure default templates are seeded
2. **WhatsApp not sending**: Check WhatsApp configuration and API credentials
3. **Variables not replaced**: Ensure variable names match exactly in template

### Logs
Check logs for WhatsApp notification issues:
```bash
tail -f storage/logs/laravel.log | grep -i whatsapp
```

## Future Enhancements
- Support for bug priority-based templates
- Template versioning
- A/B testing for templates
- Rich media support (images, documents)
- Template analytics and performance metrics
